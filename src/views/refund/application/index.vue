<template>
  <div class="app-container">
    <ComTableList
      :searchData="searchData"
      :column="columns"
      :getTableList="getList"
    >
      <template #searchButton>
        <el-button
          size="mini"
          :type="'primary'"
          icon="el-icon-plus"
          @click="gotoAdd('add', {})"
          v-hasPermi="['sales:reOrder:add']"
          >新增</el-button
        >
      </template>

      <template
        v-for="(item, index) in optionsCodeList"
        #[item.slotName]="{ row }"
      >
        <dict-tag
          :key="index"
          :options="dict.type[item.optionsCode]"
          :value="row[item.prop]"
        />
      </template>

      <template #caozuo="{ row }">
        <div style="display: flex; justify-content: space-between">
          <el-button
            v-hasPermi="['sales:reOrder:edit']"
            size="mini"
            type="primary"
            @click="gotoAdd('edit', row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['sales:reOrder:query']"
            size="mini"
            type="primary"
            @click="gotoAdd('look', row)"
            >详情</el-button
          >
        </div>
      </template>
    </ComTableList>
  </div>
</template>

<script>
import ComTableList from "@/components/ComTable/list.vue";

import {  columns, optionsCodeList ,searchData} from "./config";
import { listReOrder, getReOrder, delReOrder, addReOrder, updateReOrder } from "@/api/refund/reOrder.js";

export default {
  name: "Application",
  dicts: ['spart','vtweg','vkbur','overdue_date','return_mode','ship_method','source_type'],
  dictsKey: ["VKORG",'doct_status'],
  components: {
    ComTableList,
  },
  data() {
    return {
      searchData:searchData,
      columns,
      optionsCodeList,
    };
  },
  created() {},
  methods: {
    /** 查询销售订单头信息列表 */
    async getList(params) {
      return listReOrder({
        ...params,
      });
    },
    gotoAdd(type, data) {
      this.$store.dispatch("tagsView/delPage", { name: "ApplicationDetail" });
      this.$router.push(
        `/refund/application-detail/index/${type}/${data.zsasOrderHeaderId}`
      );
    },
  },
};
</script>

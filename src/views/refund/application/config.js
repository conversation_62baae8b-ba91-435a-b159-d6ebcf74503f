// 头部搜索区域配置
export const searchData = [
  // 第一行：SAS退货申请单号 | 客户代码 | 客户名称
  {
    label: "SAS退货申请单号",
    type: "input",
    prop: "torderSqno",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    label: "客户代码",
    type: "input",
    prop: "kunnr",
    append: true,
    dictOptionCode: "KUNNR",
    attr: {},
    backProps: [
      {
        backProp: "KUNNR",
        backLable: "KUNNR",
        prop: "kunnr",
      },
      {
        backProp: "NAME1",
        backLable: "NAME1",
        prop: "name1",
      },
    ],
  },

  {
    label: "客户名称",
    type: "input",
    prop: "name1",
    width: 200,
    attr: {},
    append: true,
    backProps: [
      {
        backProp: "NAME1",
        backLable: "NAME1",
        prop: "name1",
      },
      {
        backProp: "KUNNR",
        backLable: "KUNNR",
        prop: "kunnr",
      },
    ],
  },

  // 第二行：分销渠道 | 产品组 | 销售组织
  {
    prop: "vtweg",
    label: "分销渠道",
    type: "select",
    optionsCode: "vtweg",
    slotName: "vtweg",
  },

  {
    prop: "spart",
    label: "产品组",
    type: "select",
    optionsCode: "spart",
    slotName: "spart",
  },

  {
    prop: "vkorg",
    label: "销售组织",
    type: "input",
    optionsCode: "vkorg",
    slotName: "vkorg",
    append: true,
    attr: {},
    //  返回的字段映射
    backProps: [
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "vkorg",
      },
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
    ],
  },

  // 第三行：销售办公室 | 退货原因 | 客户类型
  {
    prop: "vkbur",
    label: "销售办公室",
    width: 140,
    type: "select",
    optionsCode: "vkbur",
  },

  {
    prop: "returnReason",
    label: "退货原因",
    type: "select",
    optionsCode: "return_reason",
    searchOut:true,
    width: 140,
  },

  {
    prop: "customerType",
    label: "客户类型",
    type: "select",
    optionsCode: "customer_type",
    width: 140,
  },

  // 第四行：单据状态 | 创建时间 | 创建人
  {
    label: "单据状态",
    type: "select",
    prop: "document_status",
    optionsCode: "doct_status",
    attr: {
      // disabled: true,
    },
  },

  {
    label: "创建时间",
    type: "date",
    prop: "createdDate",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    label: "创建人",
    type: "input",
    prop: "createdBy",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  // 第五行：审批状态 | 付款条件 | 发运方式
  {
    label: "审批状态",
    type: "select",
    prop: "releaseStatus",
    optionsCode: "release_status",
    slotName: "releaseStatus",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    label: "付款条件",
    type: "select",
    prop: "paymentTerms",
    optionsCode: "payment_terms",
    width: 140,
  },

  {
    label: "发运方式",
    type: "select",
    prop: "shipMethod",
    optionsCode: "ship_method",
    width: 140,
  },

  // 第六行：抬头备注 | 退货方式
  {
    label: "抬头备注",
    type: "input",
    prop: "hMemo",
    width: 200,
  },

  {
    prop: "returnMode",
    label: "退货方式",
    width: 140,
    type: "select",
    optionsCode: "return_mode",
  },

  // 工厂字段
  {
    label: "工厂",
    type: "input",
    prop: "werks",
    optionsCode: "werks",
    width: 140,
    append: true,
    backProps: [
      {
        backProp: "werks",
        backLable: "werksName",
        prop: "werks",
      },
      {
        backProp: "salesOrg",
        backLable: "salesOrgName",
        prop: "vkorg",
      },
    ],
  },
];

// 表格列配置
export const columns = [
  {
    prop: "itemNo",
    label: "序号",
    width: 80,
    type: "index",
  },

  {
    prop: "zzmatnr",
    label: "物料号",
    width: 140,
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        prop: "zzmatnr",
        propLabels: ["zzmatnr", "ZZMATNR"],
      },
      {
        prop: "name",
        propLabels: ["name", "maktxZh"],
      },
      {
        prop: "kein",
        propLabels: ["kein", "MEINS"],
      },
    ],
  },

  {
    prop: "name",
    label: "名称",
    width: 200,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "allowQty",
    label: "可退数量",
    width: 100,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "applyQty",
    label: "申请退货数量",
    width: 120,
    type: "number",
  },

  {
    prop: "kein",
    label: "物料单位",
    width: 100,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "netpr",
    label: "单价",
    width: 100,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "discount",
    label: "折扣",
    width: 80,
    type: "number",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "amount",
    label: "退货金额（元）",
    width: 120,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "remark",
    label: "子公司备注",
    width: 140,
    type: "input",
  },

  {
    prop: "originalType",
    label: "来源类型",
    width: 100,
    type: "select",
    optionsCode: "source_type",
  },

  {
    prop: "originalDn",
    label: "来源提货单号",
    width: 140,
    append: true,
  },

  {
    prop: "originalItem",
    label: "来源提货单行号",
    width: 120,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "originalDate",
    label: "提货日期",
    width: 100,
    type: "date",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "overdueDate",
    label: "超期范围",
    type: "select",
    optionsCode: "overdue_date",
    width: 100,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "documentNo",
    label: "整改文件号",
    width: 120,
  },

  {
    prop: "returnMode",
    label: "退货方式",
    width: 100,
    type: "select",
    optionsCode: "return_mode",
  },

  {
    prop: "returnAmount90days",
    label: "前90天已退货金额",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "approveQty",
    label: "同意退货数量",
    width: 120,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "lastYearSalesAmount",
    label: "上年度销售额",
    width: 120,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "yearSalesAmount",
    label: "本年累计销售额",
    width: 120,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "yearReturnAmount",
    label: "本年累计退货金额",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "allowReturnRate",
    label: "制度可退货比例标准",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "lgort",
    label: "库存地点",
    width: 100,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "caozuo",
    label: "操作",
    width: 140,
    slotName: "caozuo",
    hiddenSearch: true,
    fixed: "right",
  },
];

// 映射code
export const optionsCodeList = searchData.filter(({ optionsCode }) => optionsCode);

export const getListData = () => {
  const columnsObj = {};
  columns.forEach((item) => {
    columnsObj[item.prop] = item.prop;
  });

  return [columnsObj, columnsObj];
};

export const lineCloumn = [
  {
    prop: "zzmatnr",
    label: "专卖物料号",
    width: 300,
    append: true,
    attr: {
      // readonly: true,
    },
    backProps: [
      {
        prop: "zzmatnr",
        propLabels: ["zzmatnr", "ZZMATNR"],
      },
      {
        prop: "name",
        propLabels: ["name", "maktxZh"],
      },
      {
        prop: "kein",
        propLabels: ["kein", "MEINS"],
      },
    ],
  },

   {
    prop: "sourceType",
    label: "来源类型",
    width: 140,
    type: "select",
    optionsCode: "source_type",
    attr: {
      // disabled: true,
    },
    // width:140
  },


  {
    prop: "originalDn",
    label: "来源提货单号",
    width: 260,
    append: true,
  },

  {
    prop: "allowQty",
    label: "可退数量",
    width: 100,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "applyQty",
    label: "申请退货数量",
    width: 100,
  },
  {
    prop: "documentNo",
    label: "整改文件号",
    width: 140,
  },
  {
    prop: "originalItem",
    label: "来源提货单行号",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "name",
    label: "物料描述",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },
  {
    prop: "kein",
    label: "基本单位",
    width: 100,
    attr: {
      disabled: true,
    },
  },
  {
    prop: "netpr",
    label: "单价",
    width: 100,
    attr: {
      disabled: true,
    },
    // width: 140,
  },

  {
    prop: "discount",
    label: "折扣",
    width: 60,
    type: "number",
    attr: {
      disabled: true,
    },
  },

  {
    prop: "amount",
    label: "退货金额（元）",
    width: 140,
    attr: {
      disabled: true,
    },
    // width: 140,
  },

  {
    prop: "originalDate",
    label: "提货日期",
    width: 100,
    attr: {
      disabled: true,
    },
    // width:140
  },
  {
    prop: "overdueDate",
    label: "超期范围",
    type: "select",
    optionsCode: "overdue_date",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },


  {
    prop: "lgort",
    label: "库存地点",
    width: 140,
    attr: {
      disabled: true,
    },
  },

  {
    prop: "returnAmount90days",
    label: "前90天已退货金额",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },

  {
    prop: "approveQty",
    label: "同意退货数量",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },

  {
    prop: "lastYearSalesAmount",
    label: "上年度销售额",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },

  {
    prop: "yearSalesAmount",
    label: "本年累计销售额",
    attr: {
      disabled: true,
    },
    width: 200,
    // width:140
  },

  {
    prop: "yearReturnAmount",
    label: "本年累计退货金额",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },

  {
    prop: "allowReturnRate",
    label: "制度可退货比例标准",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },

  {
    prop: "returnRete",
    label: "现退货比例",
    width: 200,
    attr: {
      disabled: true,
    },
    // width:140
  },
  {
    prop: "remark",
    label: "子公司备注",
    width: 140,
    type: "input",
    attr: {},
  },
];

const applyQtyInputChange = (val, oldVal, self) => {
  const { allowQty = 0 } = self?.row;
  const num = Number(allowQty) || 1;
  if (val > num) {
    return num;
  } else if (val <= 0) {
    return 0;
  }
  return val;
};

const discountInputChange = (val, oldVal, self) => {
  if (val > 100) {
    return 100;
  } else if (val <= 0) {
    return 0;
  }
  return val;
};

const changeLineItem = (val, item, editItem, self) => {
  let approveQty = Number(editItem.approveQty) || 0;
  let applyQty = Number(editItem.applyQty) || 0;
  let discount = Number(editItem.discount) || 100;
  let netpr = Number(editItem.netpr) || 0;

  let num = approveQty || applyQty;

  let total = (discount / 100) * netpr * num;
  editItem.amount = total;
};

export const getLineCloumn = (docStatus = "") => {
  const data = JSON.parse(JSON.stringify(lineCloumn)).map((item) => {
    if (item.prop === "applyQty") {
      item.inputChange = applyQtyInputChange;
    }
    if (item.prop === "discount") {
      item.inputChange = discountInputChange;
    }
    if (["discount", "netpr", "applyQty", "approveQty"].includes(item.prop)) {
      item.changeLineItem = changeLineItem;
    }

    return item;
  });

  return data;
};

export const getDetailSearchData = (docStatus) => {
  const data = JSON.parse(JSON.stringify(searchData)).map((item) => {
    return item;
  });

  return data;
};

export const rules = {
  torderSqno: [{ required: true, message: "SAS退货申请单号不能为空", trigger: "change" }],
  kunnr: [{ required: true, message: "请选择客户代码", trigger: "change" }],
  name1: [{ required: true, message: "请选择客户名称", trigger: "change" }],
  vtweg: [{ required: true, message: "请选择分销渠道", trigger: "change" }],
  spart: [{ required: true, message: "请选择产品组", trigger: "change" }],
  vkorg: [{ required: true, message: "请选择销售组织", trigger: "change" }],
  vkbur: [{ required: true, message: "请选择销售办公室", trigger: "change" }],
  returnReason: [{ required: true, message: "请选择退货原因", trigger: "change" }],
  customerType: [{ required: true, message: "请选择客户类型", trigger: "change" }],
  paymentTerms: [{ required: true, message: "请选择付款条件", trigger: "change" }],
  shipMethod: [{ required: true, message: "请选择发运方式", trigger: "change" }],
  returnMode: [{ required: true, message: "请选择退货方式", trigger: "change" }],
  werks: [{ required: true, message: "请选择工厂", trigger: "change" }],
};

export const btnProps = {
  add: {
    isShow: true,
    hasPermi: ["Application:line:save"],
  },
  batchDelete: {
    isShow: true,
    hasPermi: ["Application:line:del"],
  },
  deleteLine: {
    isShow: true,
    hasPermi: ["Application:line:del"],
  },
  uploadExcel: {
    isShow: true,
    hasPermi: ["Application:line:upload"],
  },
};

const _btnProps = {};
Object.keys(btnProps).forEach((key) => {
  _btnProps[key] = {
    ...btnProps[key],
    isShow: false,
  };
});

export const lookBtnProps = _btnProps;
